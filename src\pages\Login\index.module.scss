.login-wrapper {
  display: grid;
  min-height: 100svh;
}

@media (min-width: 1024px) {
  .login-wrapper {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}



.login-main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-main-content-title-wrapper {
  display: flex;
  background-color: darkgray;
  width: 100%;
  margin-top: 10px;
}
.login-main-content-title {
  display: flex;
  align-items: center;
  justify-content: start;
  font-size: 26px;
  font-weight: bold;
}

.login-bg-content {
  display: none;
  background-color: #1890ff;
  height: 100svh;

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}

@media (min-width: 1024px) {
  .login-bg-content {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}


.login-form-wrapper {
  background: center/cover url("~@/assets/login.png");
  height: 500px;
  width: 400px;
  border: 0;
}

.login-logo {
  width: 200px;
  height: 60px;
  display: block;
  margin: 0 auto 20px;
}


