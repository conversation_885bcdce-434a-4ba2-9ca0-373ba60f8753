import styles from './index.module.scss'
import LoginForm from './login-form'
import PlaceholderImg from '@assets/images/background.png'
import { Icon } from "@iconify/react";
const Login = () => {


    return (
        <div className={styles['login-wrapper']}>
            <div className={styles['login-main-content']}>
                <div className={styles['login-main-content-title-wrapper']}>
                    <div className={styles['login-main-content-title']}>
                        <Icon icon="solar:code-square-bold" color='grenn' fontSize={28} />
                        <span>樱花我的世界社区 | 后台管理系统</span>
                    </div>
                </div>
                <LoginForm />
            </div>
            <div className={styles['login-bg-content']}>
                <img
                    src={PlaceholderImg}
                    alt="placeholder img"
                />
            </div>
        </div>

    )
}

export default Login